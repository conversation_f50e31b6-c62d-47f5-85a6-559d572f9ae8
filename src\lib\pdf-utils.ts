// PDF utility functions for client-side PDF processing
import * as pdfjsLib from 'pdfjs-dist';

// Set up the worker
if (typeof window !== 'undefined') {
  pdfjsLib.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjsLib.version}/pdf.worker.min.js`;
}

/**
 * Convert a PDF file to an image (first page)
 * @param file - The PDF file to convert
 * @param scale - Scale factor for rendering (default: 1.5)
 * @returns Promise that resolves to a data URL of the rendered image
 */
export async function pdfToImage(file: File, scale: number = 1.5): Promise<string> {
  try {
    // Convert file to array buffer
    const arrayBuffer = await file.arrayBuffer();
    
    // Load the PDF document
    const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;
    
    // Get the first page
    const page = await pdf.getPage(1);
    
    // Create a canvas element
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    
    if (!context) {
      throw new Error('Could not get canvas context');
    }
    
    // Calculate the viewport
    const viewport = page.getViewport({ scale });
    
    // Set canvas dimensions
    canvas.height = viewport.height;
    canvas.width = viewport.width;
    
    // Render the page
    const renderContext = {
      canvasContext: context,
      viewport: viewport,
    };
    
    await page.render(renderContext).promise;
    
    // Convert canvas to data URL
    return canvas.toDataURL('image/jpeg', 0.8);
    
  } catch (error) {
    console.error('Error converting PDF to image:', error);
    throw new Error('Failed to convert PDF to image');
  }
}

/**
 * Convert multiple PDF pages to images
 * @param file - The PDF file to convert
 * @param maxPages - Maximum number of pages to convert (default: 1)
 * @param scale - Scale factor for rendering (default: 1.5)
 * @returns Promise that resolves to an array of data URLs
 */
export async function pdfToImages(file: File, maxPages: number = 1, scale: number = 1.5): Promise<string[]> {
  try {
    // Convert file to array buffer
    const arrayBuffer = await file.arrayBuffer();
    
    // Load the PDF document
    const pdf = await pdfjsLib.getDocument({ data: arrayBuffer }).promise;
    
    const numPages = Math.min(pdf.numPages, maxPages);
    const images: string[] = [];
    
    // Convert each page to image
    for (let pageNum = 1; pageNum <= numPages; pageNum++) {
      const page = await pdf.getPage(pageNum);
      
      // Create a canvas element
      const canvas = document.createElement('canvas');
      const context = canvas.getContext('2d');
      
      if (!context) {
        throw new Error('Could not get canvas context');
      }
      
      // Calculate the viewport
      const viewport = page.getViewport({ scale });
      
      // Set canvas dimensions
      canvas.height = viewport.height;
      canvas.width = viewport.width;
      
      // Render the page
      const renderContext = {
        canvasContext: context,
        viewport: viewport,
      };
      
      await page.render(renderContext).promise;
      
      // Convert canvas to data URL and add to array
      images.push(canvas.toDataURL('image/jpeg', 0.8));
    }
    
    return images;
    
  } catch (error) {
    console.error('Error converting PDF pages to images:', error);
    throw new Error('Failed to convert PDF pages to images');
  }
}
