"use client";

import { useEffect, useState } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { CheckCircle, AlertCircle, ArrowLeft, FileText } from "lucide-react";
import { useNotifications } from "@/contexts/notification-context";
import type { Notification } from "@/contexts/notification-context";

export default function NotificationDetailsPage() {
  const params = useParams();
  const router = useRouter();
  const { notifications, markAsRead } = useNotifications();
  const [notification, setNotification] = useState<Notification | null>(null);

  useEffect(() => {
    const notificationId = params.id as string;
    const foundNotification = notifications.find(
      (n) => n.id === notificationId
    );

    if (foundNotification) {
      setNotification(foundNotification);
      // Mark as read when viewing details
      if (!foundNotification.isRead) {
        markAsRead(foundNotification.id);
      }
    }
  }, [params.id, notifications, markAsRead]);

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleString();
    } catch {
      return dateString;
    }
  };

  if (!notification) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="max-w-4xl mx-auto">
          <div className="text-center py-12">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">
              Notification Not Found
            </h3>
            <p className="text-muted-foreground mb-6">
              The notification you're looking for doesn't exist or has been
              removed.
            </p>
            <Button onClick={() => router.push("/")}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              Go Back
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <Button
            variant="outline"
            onClick={() => router.back()}
            className="mb-4"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <h1 className="text-3xl font-bold mb-2">{notification.title}</h1>
          <p className="text-muted-foreground">{notification.message}</p>
        </div>

        {/* Notification Details */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {notification.type === "success" ? (
                <CheckCircle className="h-5 w-5 text-green-500" />
              ) : notification.type === "error" ? (
                <AlertCircle className="h-5 w-5 text-red-500" />
              ) : (
                <FileText className="h-5 w-5 text-blue-500" />
              )}
              Notification Details
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label className="text-sm font-medium">Type</Label>
                <p className="text-sm text-muted-foreground capitalize">
                  {notification.type}
                </p>
              </div>
              <div>
                <Label className="text-sm font-medium">Created At</Label>
                <p className="text-sm text-muted-foreground">
                  {notification.createdAt.toLocaleString()}
                </p>
              </div>
              {notification.pdfFileName && (
                <div>
                  <Label className="text-sm font-medium">PDF File</Label>
                  <p className="text-sm text-muted-foreground">
                    {notification.pdfFileName}
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* PDF Data Details */}
        {notification.pdfData && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>PDF Content Details</CardTitle>
              <CardDescription>
                Information extracted from the uploaded PDF file
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {/* Template Information */}
                <div>
                  <h3 className="text-lg font-semibold mb-3">
                    Template Information
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <Label className="text-sm font-medium">Template ID</Label>
                      <p className="text-sm text-muted-foreground font-mono">
                        {notification.pdfData.templateId}
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">
                        Template Name
                      </Label>
                      <p className="text-sm text-muted-foreground">
                        {notification.pdfData.templateName}
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">Layout Size</Label>
                      <p className="text-sm text-muted-foreground">
                        {notification.pdfData.layoutSize}
                      </p>
                    </div>
                    <div>
                      <Label className="text-sm font-medium">
                        Generated At
                      </Label>
                      <p className="text-sm text-muted-foreground">
                        {formatDate(notification.pdfData.generatedAt)}
                      </p>
                    </div>
                  </div>
                </div>

                {/* User Data */}
                <div>
                  <h3 className="text-lg font-semibold mb-3">User Data</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {Object.entries(notification.pdfData.userData).map(
                      ([key, value]) => (
                        <div key={key}>
                          <Label className="text-sm font-medium">{key}</Label>
                          <p className="text-sm text-muted-foreground">
                            {value || <span className="italic">Empty</span>}
                          </p>
                        </div>
                      )
                    )}
                  </div>
                </div>

                {/* Embedded Photo */}
                {notification.pdfData.photoBase64 && (
                  <div>
                    <h3 className="text-lg font-semibold mb-3">
                      Embedded Photo
                    </h3>
                    <div className="max-w-xs">
                      <img
                        src={notification.pdfData.photoBase64}
                        alt="Embedded photo"
                        className="w-full h-auto border rounded-lg"
                      />
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* PDF Preview */}
        {notification.pdfUrl && (
          <Card>
            <CardHeader>
              <CardTitle>PDF Preview</CardTitle>
              <CardDescription>
                Preview of the uploaded PDF file
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="w-full h-96 border rounded-lg overflow-hidden">
                <iframe
                  src={notification.pdfUrl}
                  className="w-full h-full"
                  title="PDF Preview"
                />
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
