import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    
    if (!file) {
      return NextResponse.json(
        { error: 'PDF file is required' },
        { status: 400 }
      );
    }

    if (file.type !== 'application/pdf') {
      return NextResponse.json(
        { error: 'File must be a PDF' },
        { status: 400 }
      );
    }

    // Convert file to buffer
    const arrayBuffer = await file.arrayBuffer();
    const buffer = Buffer.from(arrayBuffer);
    
    // Convert buffer to string to search for embedded data
    const pdfText = buffer.toString('latin1');
    
    // Look for the embedded data markers
    const startMarker = 'LDIS_DATA_BEGIN:';
    const endMarker = ':LDIS_DATA_END';
    
    const startIndex = pdfText.indexOf(startMarker);
    const endIndex = pdfText.indexOf(endMarker);
    
    if (startIndex === -1 || endIndex === -1) {
      return NextResponse.json({
        success: false,
        error: 'No embedded data found in PDF',
        hasEmbeddedData: false
      });
    }
    
    // Extract the JSON data
    const jsonStart = startIndex + startMarker.length;
    const jsonData = pdfText.substring(jsonStart, endIndex);
    
    try {
      const parsedData = JSON.parse(jsonData);
      
      return NextResponse.json({
        success: true,
        hasEmbeddedData: true,
        data: parsedData
      });
    } catch (parseError) {
      console.error('Error parsing embedded JSON:', parseError);
      return NextResponse.json({
        success: false,
        error: 'Invalid embedded data format',
        hasEmbeddedData: true
      });
    }
    
  } catch (error) {
    console.error('Error parsing PDF:', error);
    return NextResponse.json(
      { error: 'Failed to parse PDF file' },
      { status: 500 }
    );
  }
}
